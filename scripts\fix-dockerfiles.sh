#!/bin/bash

# =============================================================================
# 🔧 Fix Dockerfiles for Shared Module Paths
# =============================================================================
# This script fixes all service Dockerfiles to properly copy shared modules

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Services to fix
SERVICES=("api-gateway" "link-service" "community-service" "chat-service" "news-service" "admin-service")

print_status "Fixing Dockerfiles for shared module paths..."

for service in "${SERVICES[@]}"; do
    dockerfile_path="services/$service/Dockerfile"
    
    if [ -f "$dockerfile_path" ]; then
        print_status "Fixing $dockerfile_path..."
        
        # Create backup
        cp "$dockerfile_path" "$dockerfile_path.backup"
        
        # Fix the Dockerfile
        cat > "$dockerfile_path" << 'EOF'
# Multi-stage build for Service
FROM node:18-alpine AS base

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Copy service package files first
COPY services/SERVICE_NAME/package*.json ./

# Install dependencies
FROM base AS deps
RUN npm install --only=production && npm cache clean --force

# Development stage
FROM base AS dev
# Copy service package files
COPY services/SERVICE_NAME/package*.json ./
RUN npm ci
# Copy shared utilities first
COPY shared/ ./shared/
# Copy service-specific code
COPY services/SERVICE_NAME/ ./
USER nodejs
EXPOSE SERVICE_PORT
CMD ["dumb-init", "npm", "run", "dev"]

# Production stage
FROM base AS production

# Copy production dependencies
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy shared utilities first
COPY --chown=nodejs:nodejs shared/ ./shared/

# Copy service-specific code
COPY --chown=nodejs:nodejs services/SERVICE_NAME/ ./

# Create health check script
COPY --chown=nodejs:nodejs <<HEALTH ./healthcheck.js
const http = require('http');

const options = {
  hostname: 'localhost',
  port: process.env.PORT || SERVICE_PORT,
  path: '/health/live',
  method: 'GET',
  timeout: 3000
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    process.exit(0);
  } else {
    process.exit(1);
  }
});

req.on('error', () => {
  process.exit(1);
});

req.on('timeout', () => {
  req.destroy();
  process.exit(1);
});

req.end();
HEALTH

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE SERVICE_PORT

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# Start the application
CMD ["dumb-init", "node", "src/app.js"]
EOF

        # Replace placeholders with actual service values
        case $service in
            "api-gateway")
                sed -i 's/SERVICE_NAME/api-gateway/g' "$dockerfile_path"
                sed -i 's/SERVICE_PORT/8080/g' "$dockerfile_path"
                ;;
            "link-service")
                sed -i 's/SERVICE_NAME/link-service/g' "$dockerfile_path"
                sed -i 's/SERVICE_PORT/3002/g' "$dockerfile_path"
                ;;
            "community-service")
                sed -i 's/SERVICE_NAME/community-service/g' "$dockerfile_path"
                sed -i 's/SERVICE_PORT/3003/g' "$dockerfile_path"
                ;;
            "chat-service")
                sed -i 's/SERVICE_NAME/chat-service/g' "$dockerfile_path"
                sed -i 's/SERVICE_PORT/3004/g' "$dockerfile_path"
                ;;
            "news-service")
                sed -i 's/SERVICE_NAME/news-service/g' "$dockerfile_path"
                sed -i 's/SERVICE_PORT/3005/g' "$dockerfile_path"
                ;;
            "admin-service")
                sed -i 's/SERVICE_NAME/admin-service/g' "$dockerfile_path"
                sed -i 's/SERVICE_PORT/3006/g' "$dockerfile_path"
                ;;
        esac
        
        print_success "Fixed $dockerfile_path"
    else
        print_error "Dockerfile not found: $dockerfile_path"
    fi
done

print_success "All Dockerfiles fixed!"
print_status "Backups created with .backup extension"
print_status "You can now rebuild the Docker images"
