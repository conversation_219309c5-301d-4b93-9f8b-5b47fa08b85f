#!/bin/bash

# =============================================================================
# 🔧 Fix Import Paths for Docker Container
# =============================================================================
# This script fixes import paths from ../shared to ../../shared for Docker

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Services to fix
SERVICES=("auth-service" "api-gateway" "link-service" "community-service" "chat-service" "news-service" "admin-service")

print_status "Fixing import paths for Docker containers..."

for service in "${SERVICES[@]}"; do
    service_path="services/$service"
    
    if [ -d "$service_path" ]; then
        print_status "Fixing import paths in $service..."
        
        # Find all JavaScript files and fix import paths
        find "$service_path" -name "*.js" -type f | while read -r file; do
            if grep -q "../shared" "$file"; then
                print_status "  Fixing $file"
                
                # Create backup
                cp "$file" "$file.backup"
                
                # Replace ../shared with ../../shared
                sed -i 's|../shared|../../shared|g' "$file"
                
                print_success "  Fixed $file"
            fi
        done
        
        print_success "Fixed import paths in $service"
    else
        print_error "Service directory not found: $service_path"
    fi
done

print_success "All import paths fixed!"
print_status "Backups created with .backup extension"
